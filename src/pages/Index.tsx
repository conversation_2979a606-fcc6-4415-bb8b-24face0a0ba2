
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import ProductDemo from '@/components/ProductDemo';
import RoleSelectionModal from '@/components/RoleSelectionModal';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { Button } from '@/components/ui/button';

import { Mail, Phone, MapPin, Github, Twitter, Linkedin, Star, TrendingUp, Shield, Brain, BarChart3, Users, Eye, Lightbulb, Target } from 'lucide-react';

const Index = () => {
  const { t } = useTranslation();
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);

  const handleJoinUs = () => {
    setIsRoleModalOpen(true);
  };

  const handleStartDiscovering = () => {
    // Scroll to contact section
    const contactSection = document.querySelector('footer');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header onJoinNowClick={handleJoinUs} />

      {/* 3D Crystal Hero Section */}
      <HeroSection onJoinNowClick={handleJoinUs} />

      {/* Key Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              {t('homepage.features.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {t('homepage.features.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Feature Card 1 */}
            <div className="group bg-white p-8 rounded-xl border border-gray-200 hover:border-[#40826D]/30 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#40826D] to-[#008C8C] rounded-xl flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{t('homepage.features.aiFounderAnalysis.title')}</h3>
              <p className="text-gray-600 leading-relaxed">
                {t('homepage.features.aiFounderAnalysis.description')}
              </p>
            </div>

            {/* Feature Card 2 */}
            <div className="group bg-white p-8 rounded-xl border border-gray-200 hover:border-[#40826D]/30 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#40826D] to-[#008C8C] rounded-xl flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{t('homepage.features.marketIntelligence.title')}</h3>
              <p className="text-gray-600 leading-relaxed">
                {t('homepage.features.marketIntelligence.description')}
              </p>
            </div>

            {/* Feature Card 3 */}
            <div className="group bg-white p-8 rounded-xl border border-gray-200 hover:border-[#40826D]/30 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#40826D] to-[#008C8C] rounded-xl flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{t('homepage.features.riskAssessment.title')}</h3>
              <p className="text-gray-600 leading-relaxed">
                {t('homepage.features.riskAssessment.description')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Clean Footer */}
      <footer className="relative py-12 overflow-hidden">
        {/* Dark gradient background matching hero */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black"></div>

        {/* Subtle viridian accents */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#40826D]/10 to-transparent"></div>
        </div>

        {/* Subtle grid pattern */}
        <div
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `linear-gradient(rgba(64, 130, 109, 0.1) 1px, transparent 1px),
                             linear-gradient(90deg, rgba(64, 130, 109, 0.1) 1px, transparent 1px)`,
            backgroundSize: '50px 50px'
          }}
        ></div>
        
        <div className="relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 flex items-center justify-center">
                  <img
                    src="/logo.png"
                    alt="Veridian Vista Logo"
                    className="w-8 h-8 object-contain drop-shadow-lg"
                    onError={(e) => {
                      // Fallback to a simple text logo if image fails to load
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.parentElement!.innerHTML = '<div class="text-white font-bold text-lg">VV</div>';
                    }}
                  />
                </div>
                <h3 className="text-2xl font-bold text-white">Veridian Vista</h3>
              </div>
              <p className="text-white/80 mb-6 max-w-md">
                {t('footer.description')}
              </p>
              {/* <div className="flex space-x-4">
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <Twitter className="w-6 h-6" />
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <Linkedin className="w-6 h-6" />
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <Github className="w-6 h-6" />
                </a>
              </div> */}
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">{t('footer.product')}</h4>
              <ul className="space-y-3">
                <li>
                  <a href="/investor" className="text-white/80 hover:text-white transition-colors">
                    {t('footer.forInvestors')}
                  </a>
                </li>
                <li>
                  <a href="/founder" className="text-white/80 hover:text-white transition-colors">
                    {t('footer.forFounders')}
                  </a>
                </li>
                <li>
                  <a href="/about" className="text-white/80 hover:text-white transition-colors">
                    {t('footer.aboutUs')}
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">{t('footer.contact')}</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-white" />
                  <span className="text-white/80"><EMAIL></span>
                </div>
                {/* <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-[#40826D]" />
                  <span className="text-gray-600">+****************</span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="w-4 h-4 text-[#40826D]" />
                  <span className="text-gray-600">Palo Alto, CA</span>
                </div> */}
              </div>
            </div>
          </div>

          <div className="border-t border-white/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-white/60 text-sm mb-4 md:mb-0">
                © 2025 Veridian Vista. All rights reserved.
              </p>
              <div className="flex space-x-6 text-sm">
                <a href="/privacy-policy" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.privacyPolicy')}
                </a>
                <a href="/terms-of-service" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.termsOfService')}
                </a>
              </div>
            </div>
          </div>
          </div>
        </div>
      </footer>

      {/* Role Selection Modal */}
      <RoleSelectionModal
        isOpen={isRoleModalOpen}
        onClose={() => setIsRoleModalOpen(false)}
      />
    </div>
  );
};

export default Index;

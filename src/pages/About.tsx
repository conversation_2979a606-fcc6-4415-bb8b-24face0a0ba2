
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Header from '@/components/Header';
import RoleSelectionModal from '@/components/RoleSelectionModal';
import { Linkedin, Mail } from 'lucide-react';

const About = () => {
  const { t } = useTranslation();
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);

  const handleJoinNow = () => {
    setIsRoleModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-white">
      <Header onJoinNowClick={handleJoinNow} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{t('about.title')}</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('about.subtitle')}
          </p>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">{t('about.ourStory.title')}</h2>
          <div className="max-w-4xl mx-auto text-gray-600 space-y-6">
            <p>
              {t('about.ourStory.paragraph1')}
            </p>
            <p>
              {t('about.ourStory.paragraph2')}
            </p>
            <p>
              {t('about.ourStory.paragraph3')}
            </p>
          </div>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">{t('about.ourValues.title')}</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-white border border-gray-200 shadow-lg text-center">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-[#40826D] mb-3">{t('about.ourValues.innovation.title')}</h3>
                <p className="text-gray-600">
                  {t('about.ourValues.innovation.description')}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg text-center">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-[#40826D] mb-3">{t('about.ourValues.transparency.title')}</h3>
                <p className="text-gray-600">
                  {t('about.ourValues.transparency.description')}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg text-center">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-[#40826D] mb-3">{t('about.ourValues.impact.title')}</h3>
                <p className="text-gray-600">
                  {t('about.ourValues.impact.description')}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Leadership section hidden as requested */}
        {/* <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">{t('about.leadership.title')}</h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <Card className="bg-white border border-gray-200 shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="w-20 h-20 bg-[#40826D]/10 border border-[#40826D]/30 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-xl font-bold text-[#40826D]">JS</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">{t('about.leadership.janeSmith.name')}</h3>
                <p className="text-[#40826D] mb-2">{t('about.leadership.janeSmith.title')}</p>
                <p className="text-sm text-gray-600 mb-4">
                  {t('about.leadership.janeSmith.description')}
                </p>
                <div className="flex justify-center space-x-2">
                  <Linkedin className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                  <Mail className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="w-20 h-20 bg-[#40826D]/10 border border-[#40826D]/30 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-xl font-bold text-[#40826D]">MC</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">{t('about.leadership.michaelChen.name')}</h3>
                <p className="text-[#40826D] mb-2">{t('about.leadership.michaelChen.title')}</p>
                <p className="text-sm text-gray-600 mb-4">
                  {t('about.leadership.michaelChen.description')}
                </p>
                <div className="flex justify-center space-x-2">
                  <Linkedin className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                  <Mail className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="w-20 h-20 bg-[#40826D]/10 border border-[#40826D]/30 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-xl font-bold text-[#40826D]">EP</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">{t('about.leadership.emilyPark.name')}</h3>
                <p className="text-[#40826D] mb-2">{t('about.leadership.emilyPark.title')}</p>
                <p className="text-sm text-gray-600 mb-4">
                  {t('about.leadership.emilyPark.description')}
                </p>
                <div className="flex justify-center space-x-2">
                  <Linkedin className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                  <Mail className="w-5 h-5 text-gray-400 hover:text-[#40826D] cursor-pointer transition-colors" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div> */}

        {/* <div className="text-center bg-gray-50 border border-gray-200 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Join Our Mission</h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            We're always looking for talented individuals who share our passion for transforming investment discovery.
            If you're interested in joining our team, we'd love to hear from you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-[#40826D] text-white rounded-md hover:bg-[#40826D]/90 transition-all duration-300 font-semibold"
            >
              View Open Positions
            </a>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 border border-[#40826D] text-[#40826D] rounded-md hover:bg-[#40826D]/10 transition-all duration-300 font-semibold"
            >
              Contact Us
            </a>
          </div>
        </div> */}
      </div>

      {/* Role Selection Modal */}
      <RoleSelectionModal
        isOpen={isRoleModalOpen}
        onClose={() => setIsRoleModalOpen(false)}
      />
    </div>
  );
};

export default About;

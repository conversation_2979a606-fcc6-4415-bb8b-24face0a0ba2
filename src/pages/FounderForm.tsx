import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import Header from '@/components/Header';
import RoleSelectionModal from '@/components/RoleSelectionModal';
import SuccessModal from '@/components/SuccessModal';
import {
  User,
  Mail,
  Building,
  Globe,
  Target,
  DollarSign,
  MessageSquare,
  CheckCircle,
  Lightbulb,
  MapPin,
  Linkedin,
  Github,
  Bird,
  MessageCircle,
  Users
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { nocoDBService } from '@/services/nocodb';
import { emailService } from '@/services/email';

interface FounderFormData {
  // Personal Information (Required fields from NocoDB)
  founder_name: string;                    // T - Single line text
  founder_email: string;                   // Email field
  in_mailinglist: boolean;                 // Switch (true/false)
  founder_linkedin_url: string;            // URL field
  founder_github_url: string;              // URL field
  founder_twitter_url: string;             // URL field
  founder_location: string;                // Geo data field

  // Project Information (Required fields from NocoDB)
  project_name: string;                    // T - Single line text
  project_website_url: string;             // URL field
  project_oneliner: string;                // T - Single line text
  project_description: string;             // Long text field
  project_stage: string;                   // Single selection
  industry_verticals: string[];            // Multi-selection
  current_traction_data: string;           // Long text field
  funding_needs: string;                   // Single selection
  source: string;                          // T - Single line text

  // Chinese version specific fields (optional)
  founder_wechat_id_cn: string;            // T - Single line text
  in_wechat_group: boolean;                // Switch (true/false)
  project_wechat_official_account_cn: string; // T - Single line text
  project_showcase_url_cn: string;         // URL field
  developer_community_url_cn: string;      // URL field
}

const FounderForm = () => {
  const { t, i18n } = useTranslation();
  const isChineseVersion = i18n.language === 'zh';
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);

  const handleJoinNow = () => {
    setIsRoleModalOpen(true);
  };

  // Helper function to get label class based on language
  const getLabelClass = (baseClass: string = "text-gray-700") => {
    return `${baseClass} ${isChineseVersion ? 'font-semibold' : 'font-medium'}`;
  };

  const [formData, setFormData] = useState<FounderFormData>({
    // Personal Information
    founder_name: '',
    founder_email: '',
    in_mailinglist: false,
    founder_linkedin_url: '',
    founder_github_url: '',
    founder_twitter_url: '',
    founder_location: '',

    // Project Information
    project_name: '',
    project_website_url: '',
    project_oneliner: '',
    project_description: '',
    project_stage: '',
    industry_verticals: [],
    current_traction_data: '',
    funding_needs: '',
    source: '',

    // Chinese version specific fields
    founder_wechat_id_cn: '',
    in_wechat_group: false,
    project_wechat_official_account_cn: '',
    project_showcase_url_cn: '',
    developer_community_url_cn: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const projectStages = [
    'idea',
    'mvp',
    'early_traction',
    'seed',
    'series_a',
    'series_b',
    'growth'
  ];

  const industryVerticals = [
    'ai',
    'saas',
    'fintech',
    'biotech',
    'dev_tool',
    'social_media',
    'e_commerce',
    'edutech',
    'enterprise',
    'robotics',
    'hardware',
    'healthcare',
    'proptech',
    'cleantech',
    'mobility',
    'consumer',
    'blockchain',
    'cybersecurity',
    'entertainment'
  ];

  const fundingOptions = [
    'raising_funds',
    'plan_to_raise',
    'no_plan'
  ];

  const handleInputChange = (field: keyof FounderFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayFieldChange = (field: 'industry_verticals', value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked
        ? [...prev[field], value]
        : prev[field].filter(item => item !== value)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      const requiredFields = [
        'founder_name',
        'founder_email',
        'founder_location',
        ...(isChineseVersion ? [] : ['founder_linkedin_url']), // LinkedIn only required for non-Chinese version
        'project_name',
        'project_website_url',
        'project_oneliner',
        'project_description',
        'project_stage',
        'funding_needs'
      ];

      // Check for missing required fields
      const missingFields = requiredFields.filter(field => {
        const value = formData[field as keyof FounderFormData];
        return !value || (typeof value === 'string' && value.trim() === '') || (Array.isArray(value) && value.length === 0);
      });

      if (missingFields.length > 0) {
        toast.error(t('message.missingRequiredFields'));
        setIsSubmitting(false);
        return;
      }

      // Check industry verticals
      if (formData.industry_verticals.length === 0) {
        toast.error(t('message.selectAtLeastOneIndustry'));
        setIsSubmitting(false);
        return;
      }

      // Note: All Chinese-specific fields are optional based on the form UI
      // No additional required field validation needed for Chinese version

      // Set source internally for founder form submissions
      const submissionData = {
        ...formData,
        source: 'Founder Form'
      };

      // Submit to NocoDB
      console.log('Founder form submitted:', submissionData);

      const result = await nocoDBService.submitFounderData(submissionData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to submit data');
      }

      // Send email notifications
      try {
        // Send admin notification
        await emailService.sendAdminNotification({
          type: 'founder',
          data: submissionData
        });

        // Send confirmation email to founder with language detection
        await emailService.sendConfirmationEmail(
          submissionData.founder_email,
          'founder',
          submissionData.founder_name,
          i18n.language as 'en' | 'zh'
        );
      } catch (emailError) {
        console.error('Email notification failed:', emailError);
        // Don't fail the form submission if email fails
      }

      setShowSuccessModal(true);

      // Reset form
      setFormData({
        // Personal Information
        founder_name: '',
        founder_email: '',
        in_mailinglist: false,
        founder_linkedin_url: '',
        founder_github_url: '',
        founder_twitter_url: '',
        founder_location: '',

        // Project Information
        project_name: '',
        project_website_url: '',
        project_oneliner: '',
        project_description: '',
        project_stage: '',
        industry_verticals: [],
        current_traction_data: '',
        funding_needs: '',
        source: '',

        // Chinese version specific fields
        founder_wechat_id_cn: '',
        in_wechat_group: false,
        project_wechat_official_account_cn: '',
        project_showcase_url_cn: '',
        developer_community_url_cn: '',
      });
      
    } catch (error) {
      toast.error(t('message.error'));
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header onJoinNowClick={handleJoinNow} />

      {/* Main Content */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-semibold text-gray-900 mb-6">{t('founderForm.title')}</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t('founderForm.subtitle')}
            </p>
          </div>

          <Card className="bg-white shadow-2xl border-0 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] text-white p-8 relative overflow-hidden shadow-lg">
              <div className="relative z-10">
              <CardTitle className="text-3xl flex items-center justify-center space-x-3">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <Lightbulb className="w-7 h-7" />
                </div>
                <span>{t('founderForm.headerTitle')}</span>
              </CardTitle>
              <p className="text-center text-white/90 text-lg mt-3">
                {t('founderForm.headerSubtitle')}
              </p>
              <p className="text-center text-white/90 text-sm mt-4">
                <span className="text-red-300">*</span> {t('form.requiredFieldsNote')}
              </p>
              </div>
            </CardHeader>

          <CardContent className="p-12">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Personal Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <User className="w-5 h-5 text-[#40826D]" />
                  <span>{t('founderForm.personalInfo')}</span>
                </h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="founder_name" className={getLabelClass()}>
                      {t('founderForm.name')} <span className="text-red-500">{t('form.required')}</span>
                    </Label>
                    <Input
                      id="founder_name"
                      value={formData.founder_name}
                      onChange={(e) => handleInputChange('founder_name', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('placeholder.name')}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="founder_email" className={`${getLabelClass()} flex items-center space-x-1`}>
                        <Mail className="w-4 h-4" />
                        <span>{t('founderForm.email')} <span className="text-red-500">{t('form.required')}</span></span>
                      </Label>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="in_mailinglist"
                          checked={formData.in_mailinglist}
                          onCheckedChange={(checked) => handleInputChange('in_mailinglist', checked)}
                        />
                        <Label htmlFor="in_mailinglist" className={`${getLabelClass()} cursor-pointer text-sm`}>
                          {t('founderForm.inMailinglist')}
                        </Label>
                      </div>
                    </div>
                    <Input
                      id="founder_email"
                      type="email"
                      value={formData.founder_email}
                      onChange={(e) => handleInputChange('founder_email', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('placeholder.email')}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="founder_linkedin_url" className={`${getLabelClass()} flex items-center space-x-1`}>
                      <Linkedin className="w-4 h-4" />
                      <span>
                        {t('founderForm.linkedin')}
                        {isChineseVersion ? (
                          <span className="text-gray-500"> {t('form.optional')}</span>
                        ) : (
                          <span className="text-red-500"> {t('form.required')}</span>
                        )}
                      </span>
                    </Label>
                    <Input
                      id="founder_linkedin_url"
                      type="url"
                      value={formData.founder_linkedin_url}
                      onChange={(e) => handleInputChange('founder_linkedin_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('placeholder.linkedin')}
                      required={!isChineseVersion}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="founder_github_url" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Github className="w-4 h-4" />
                      <span>{t('founderForm.github')}</span>
                    </Label>
                    <Input
                      id="founder_github_url"
                      type="url"
                      value={formData.founder_github_url}
                      onChange={(e) => handleInputChange('founder_github_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('placeholder.github')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="founder_twitter_url" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Bird className="w-4 h-4" />
                      <span>{t('founderForm.twitter')}</span>
                    </Label>
                    <Input
                      id="founder_twitter_url"
                      type="url"
                      value={formData.founder_twitter_url}
                      onChange={(e) => handleInputChange('founder_twitter_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('placeholder.twitter')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="founder_location" className="text-gray-700 font-medium flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>{t('founderForm.location')} <span className="text-red-500">{t('form.required')}</span></span>
                    </Label>
                    <Input
                      id="founder_location"
                      value={formData.founder_location}
                      onChange={(e) => handleInputChange('founder_location', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('placeholder.location')}
                      required
                    />
                  </div>


                </div>
              </div>

              {/* Project Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <Building className="w-5 h-5 text-[#40826D]" />
                  <span>{t('founderForm.projectInfo')}</span>
                </h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="project_name" className="text-gray-700 font-medium">
                      {t('founderForm.projectName')} <span className="text-red-500">{t('form.required')}</span>
                    </Label>
                    <Input
                      id="project_name"
                      value={formData.project_name}
                      onChange={(e) => handleInputChange('project_name', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('placeholder.projectName')}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="project_website_url" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Globe className="w-4 h-4" />
                      <span>{t('founderForm.projectWebsite')} <span className="text-red-500">{t('form.required')}</span></span>
                    </Label>
                    <Input
                      id="project_website_url"
                      type="url"
                      value={formData.project_website_url}
                      onChange={(e) => handleInputChange('project_website_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder={t('placeholder.projectWebsite')}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="project_stage" className={getLabelClass()}>
                      {t('founderForm.projectStage')} <span className="text-red-500">{t('form.required')}</span>
                    </Label>
                    <Select value={formData.project_stage} onValueChange={(value) => handleInputChange('project_stage', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                        <SelectValue placeholder={t('form.selectStage')} />
                      </SelectTrigger>
                      <SelectContent>
                        {projectStages.map((stage) => (
                          <SelectItem key={stage} value={stage}>
                            {t(`projectStage.${stage}`)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="funding_needs" className="text-gray-700 font-medium flex items-center space-x-1">
                      <DollarSign className="w-4 h-4" />
                      <span>{t('founderForm.fundingNeeds')} <span className="text-red-500">{t('form.required')}</span></span>
                    </Label>
                    <Select value={formData.funding_needs} onValueChange={(value) => handleInputChange('funding_needs', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                        <SelectValue placeholder={t('form.selectFunding')} />
                      </SelectTrigger>
                      <SelectContent>
                        {fundingOptions.map((option) => (
                          <SelectItem key={option} value={option}>
                            {t(`funding.${option}`)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="project_oneliner" className="text-gray-700 font-medium">
                    {t('founderForm.projectOneliner')} <span className="text-red-500">{t('form.required')}</span>
                  </Label>
                  <Input
                    id="project_oneliner"
                    value={formData.project_oneliner}
                    onChange={(e) => handleInputChange('project_oneliner', e.target.value)}
                    className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                    placeholder={t('placeholder.projectOneliner')}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="project_description" className="text-gray-700 font-medium">
                    {t('founderForm.projectDescription')} <span className="text-red-500">{t('form.required')}</span>
                  </Label>
                  <Textarea
                    id="project_description"
                    value={formData.project_description}
                    onChange={(e) => handleInputChange('project_description', e.target.value)}
                    className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                    placeholder={t('placeholder.projectDescription')}
                    rows={4}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="current_traction_data" className="text-gray-700 font-medium">
                    {t('founderForm.currentTraction')} {t('form.optional')}
                  </Label>
                  <Textarea
                    id="current_traction_data"
                    value={formData.current_traction_data}
                    onChange={(e) => handleInputChange('current_traction_data', e.target.value)}
                    className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                    placeholder={t('placeholder.currentTraction')}
                    rows={3}
                  />
                </div>
              </div>

              {/* Industry Verticals */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <Target className="w-5 h-5 text-[#40826D]" />
                  <span>{t('founderForm.industryVerticals')} <span className="text-red-500">{t('form.required')}</span></span>
                </h3>

                <div className="space-y-3">
                  <Label className={getLabelClass()}>{t('founderForm.industryVerticals')}</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {industryVerticals.map((vertical) => (
                      <div key={vertical} className="flex items-center space-x-2">
                        <Checkbox
                          id={`vertical-${vertical}`}
                          checked={formData.industry_verticals.includes(vertical)}
                          onCheckedChange={(checked) =>
                            handleArrayFieldChange('industry_verticals', vertical, checked as boolean)
                          }
                        />
                        <Label htmlFor={`vertical-${vertical}`} className="text-sm text-gray-600">
                          {t(`industry.${vertical}`)}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Additional Information - Only for Chinese version */}
              {isChineseVersion && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                    <MessageSquare className="w-5 h-5 text-[#40826D]" />
                    <span>{t('founderForm.additionalInfo')}</span>
                  </h3>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="founder_wechat_id_cn" className="text-gray-700 font-medium flex items-center space-x-1">
                          <MessageCircle className="w-4 h-4" />
                          <span>{t('founderForm.wechatId')} (仅用于与您联系，不公开)</span>
                        </Label>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="in_wechat_group"
                            checked={formData.in_wechat_group}
                            onCheckedChange={(checked) => handleInputChange('in_wechat_group', checked)}
                          />
                          <Label htmlFor="in_wechat_group" className={`${getLabelClass()} cursor-pointer text-sm`}>
                            {t('founderForm.inWechatGroup')}
                          </Label>
                        </div>
                      </div>
                      <Input
                        id="founder_wechat_id_cn"
                        value={formData.founder_wechat_id_cn}
                        onChange={(e) => handleInputChange('founder_wechat_id_cn', e.target.value)}
                        className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                        placeholder={t('placeholder.wechatId')}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="project_wechat_official_account_cn" className="text-gray-700 font-medium flex items-center space-x-1">
                        <Users className="w-4 h-4" />
                        <span>{t('founderForm.projectWechatAccount')}</span>
                      </Label>
                      <Input
                        id="project_wechat_official_account_cn"
                        value={formData.project_wechat_official_account_cn}
                        onChange={(e) => handleInputChange('project_wechat_official_account_cn', e.target.value)}
                        className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                        placeholder={t('placeholder.projectWechatAccount')}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="project_showcase_url_cn" className="text-gray-700 font-medium flex items-center space-x-1">
                        <Globe className="w-4 h-4" />
                        <span>{t('founderForm.projectShowcaseUrl')}</span>
                      </Label>
                      <Input
                        id="project_showcase_url_cn"
                        type="url"
                        value={formData.project_showcase_url_cn}
                        onChange={(e) => handleInputChange('project_showcase_url_cn', e.target.value)}
                        className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                        placeholder={t('placeholder.projectShowcaseUrl')}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="developer_community_url_cn" className="text-gray-700 font-medium flex items-center space-x-1">
                        <Globe className="w-4 h-4" />
                        <span>{t('founderForm.developerCommunityUrl')}</span>
                      </Label>
                      <Input
                        id="developer_community_url_cn"
                        type="url"
                        value={formData.developer_community_url_cn}
                        onChange={(e) => handleInputChange('developer_community_url_cn', e.target.value)}
                        className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                        placeholder={t('placeholder.developerCommunityUrl')}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <div className="flex flex-col items-center pt-8 space-y-4">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] hover:from-[#2D5A4A] hover:to-[#40826D] text-white px-12 py-3 text-lg font-semibold"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      {t('form.submitting')}
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-5 h-5 mr-2" />
                      {t('form.submit')}
                    </>
                  )}
                </Button>

                <div className="text-sm text-gray-600 text-center">
                  {t('form.agreementText')}{' '}
                  <a href="/privacy-policy" className="text-[#40826D] hover:underline" target="_blank" rel="noopener noreferrer">
                    {t('footer.privacyPolicy')}
                  </a>{' '}
                  {t('form.and')}{' '}
                  <a href="/terms-of-service" className="text-[#40826D] hover:underline" target="_blank" rel="noopener noreferrer">
                    {t('footer.termsOfService')}
                  </a>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
        </div>
      </section>

      {/* Role Selection Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        type="founder"
      />

      <RoleSelectionModal
        isOpen={isRoleModalOpen}
        onClose={() => setIsRoleModalOpen(false)}
      />
    </div>
  );
};

export default FounderForm;

import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { gsap } from 'gsap';

// Pure Three.js Crystal Gem Component
const CrystalGem: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const crystalRef = useRef<THREE.Group | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Camera setup - wider view to show entire structure
    const camera = new THREE.PerspectiveCamera(
      60, // Wider field of view
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 0, 12); // Further back to show particles

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance"
    });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 3.0; // High exposure for ultra-brilliant effect like reference
    renderer.outputColorSpace = THREE.SRGBColorSpace;
    renderer.physicallyCorrectLights = true;
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create crystal group
    const crystalGroup = new THREE.Group();
    crystalRef.current = crystalGroup;
    scene.add(crystalGroup);

    // Create irregular organic gem geometry
    const createIrregularGemGeometry = () => {
      const geometry = new THREE.BufferGeometry();

      // Irregular gem vertices with organic, non-uniform shape
      const vertices = [
        // Top cluster (irregular crown)
        0, 3.2, 0.2,      // 0 - main peak
        0.8, 2.8, 0.5,    // 1
        -0.6, 2.9, -0.3,  // 2
        0.3, 2.7, -0.8,   // 3
        -0.4, 2.6, 0.7,   // 4

        // Upper middle ring (irregular)
        2.1, 1.2, 0.3,    // 5
        1.4, 1.5, 1.8,    // 6
        -0.2, 1.8, 2.0,   // 7
        -1.6, 1.3, 1.2,   // 8
        -2.0, 0.9, -0.1,  // 9
        -1.3, 1.1, -1.7,  // 10
        0.4, 1.4, -2.1,   // 11
        1.7, 1.0, -1.4,   // 12

        // Lower middle ring (wider, irregular)
        2.3, -0.5, 0.6,   // 13
        1.8, -0.3, 2.1,   // 14
        -0.3, -0.1, 2.4,  // 15
        -1.9, -0.4, 1.5,  // 16
        -2.2, -0.8, -0.2, // 17
        -1.6, -0.6, -1.9, // 18
        0.2, -0.2, -2.3,  // 19
        1.9, -0.7, -1.7,  // 20

        // Bottom cluster (irregular base)
        1.1, -2.1, 0.4,   // 21
        -0.7, -2.3, 0.8,  // 22
        0.5, -2.5, -0.6,  // 23
        -0.3, -2.8, -0.2, // 24
        0, -3.1, 0.1      // 25 - bottom point
      ];

      // Create irregular triangular faces
      const indices = [
        // Top crown facets (irregular)
        0, 1, 5,   0, 5, 12,  0, 12, 3,   0, 3, 2,
        0, 2, 4,   0, 4, 8,   0, 8, 9,    0, 9, 10,
        0, 10, 11, 0, 11, 12, 0, 1, 6,    0, 6, 7,
        0, 7, 8,   0, 4, 7,

        // Upper to middle connections
        1, 5, 6,   5, 13, 6,  6, 13, 14,  6, 14, 7,
        7, 14, 15, 7, 15, 8,  8, 15, 16,  8, 16, 9,
        9, 16, 17, 9, 17, 10, 10, 17, 18, 10, 18, 11,
        11, 18, 19, 11, 19, 12, 12, 19, 20, 12, 20, 5,
        5, 20, 13,

        // Middle to lower connections
        13, 20, 21, 13, 21, 14, 14, 21, 22, 14, 22, 15,
        15, 22, 24, 15, 24, 16, 16, 24, 17, 17, 24, 23,
        17, 23, 18, 18, 23, 19, 19, 23, 21, 19, 21, 20,

        // Bottom facets
        21, 23, 25, 22, 21, 25, 24, 22, 25, 23, 24, 25
      ];

      geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
      geometry.setIndex(indices);
      geometry.computeVertexNormals();

      return geometry;
    };

    // Create flowing data code texture
    const createDataCodeTexture = () => {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d')!;
      canvas.width = 1024;
      canvas.height = 1024;

      // Dark transparent base
      context.fillStyle = 'rgba(0, 0, 0, 0.1)';
      context.fillRect(0, 0, canvas.width, canvas.height);

      // Create flowing data streams
      const codeChars = '01ABCDEFabcdef{}[]()<>+-*/=';
      context.font = '12px monospace';

      // Multiple data streams
      for (let stream = 0; stream < 20; stream++) {
        const x = (stream * 50) % canvas.width;
        const hue = 120 + Math.random() * 60; // Green spectrum

        for (let i = 0; i < 80; i++) {
          const y = (i * 15) % canvas.height;
          const char = codeChars[Math.floor(Math.random() * codeChars.length)];
          const alpha = Math.random() * 0.8 + 0.2;
          const brightness = Math.random() * 60 + 40;

          context.fillStyle = `hsla(${hue}, 80%, ${brightness}%, ${alpha})`;
          context.fillText(char, x, y);
        }
      }

      // Add glowing particles with emerald colors
      for (let i = 0; i < 100; i++) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        const size = Math.random() * 3 + 1;
        const hue = 140 + Math.random() * 30; // Emerald green range

        context.beginPath();
        context.arc(x, y, size, 0, Math.PI * 2);
        context.fillStyle = `hsla(${hue}, 80%, 50%, 0.5)`; // More natural emerald tones
        context.fill();
      }

      const texture = new THREE.CanvasTexture(canvas);
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.needsUpdate = true;
      return texture;
    };

    // Create premium glass material for diamond with true brilliance
    const dataTexture = createDataCodeTexture();

    // Create high-quality environment map for realistic reflections
    const createEnvironmentCube = () => {
      const cubeRenderTarget = new THREE.WebGLCubeRenderTarget(1024, {
        format: THREE.RGBAFormat,
        generateMipmaps: true,
        minFilter: THREE.LinearMipmapLinearFilter
      });

      const cubeCamera = new THREE.CubeCamera(0.1, 1000, cubeRenderTarget);

      // Create rich environment scene for reflections
      const envScene = new THREE.Scene();

      // Gradient background for realistic reflections
      const gradientGeometry = new THREE.SphereGeometry(500, 32, 32);
      const gradientMaterial = new THREE.ShaderMaterial({
        uniforms: {
          topColor: { value: new THREE.Color('#001122') },
          bottomColor: { value: new THREE.Color('#004455') },
          offset: { value: 33 },
          exponent: { value: 0.6 }
        },
        vertexShader: `
          varying vec3 vWorldPosition;
          void main() {
            vec4 worldPosition = modelMatrix * vec4(position, 1.0);
            vWorldPosition = worldPosition.xyz;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
          }
        `,
        fragmentShader: `
          uniform vec3 topColor;
          uniform vec3 bottomColor;
          uniform float offset;
          uniform float exponent;
          varying vec3 vWorldPosition;
          void main() {
            float h = normalize(vWorldPosition + offset).y;
            gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
          }
        `,
        side: THREE.BackSide
      });

      const gradientSphere = new THREE.Mesh(gradientGeometry, gradientMaterial);
      envScene.add(gradientSphere);

      // Add environment lights
      const envLight1 = new THREE.DirectionalLight('#ffffff', 1.0);
      envLight1.position.set(100, 100, 100);
      envScene.add(envLight1);

      const envLight2 = new THREE.DirectionalLight('#50C878', 0.4);
      envLight2.position.set(-100, -100, -100);
      envScene.add(envLight2);

      cubeCamera.update(renderer, envScene);
      return cubeRenderTarget.texture;
    };

    const envMap = createEnvironmentCube();

    // Realistic emerald crystal material with natural deep green color
    const gemMaterial = new THREE.MeshPhysicalMaterial({
      color: new THREE.Color('#006C46'), // Deep emerald green like real emeralds
      metalness: 0.0,
      roughness: 0.05, // Slight roughness for more natural look
      transmission: 0.85, // Slightly less transparent for richer color
      thickness: 0.2, // Thicker for better color depth
      ior: 1.57, // Emerald's actual refractive index
      reflectivity: 0.9,
      clearcoat: 1.0,
      clearcoatRoughness: 0.02,
      transparent: true,
      opacity: 0.95,
      side: THREE.DoubleSide,
      envMap: envMap,
      envMapIntensity: 8.0, // Reduced for more natural reflections
      sheen: 0.8,
      sheenColor: new THREE.Color('#50C878'), // Lighter emerald green for sheen
      sheenRoughness: 0.1,
      // Reduced iridescence for more natural emerald look
      iridescence: 0.3,
      iridescenceIOR: 1.3,
      iridescenceThicknessRange: [200, 600],
      // Subtle emerald glow
      emissive: new THREE.Color('#004d33'),
      emissiveIntensity: 0.1
    });

    // Create edge glow material for rim lighting effect
    const edgeGlowMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        glowColor: { value: new THREE.Color('#50C878') }, // Natural emerald glow
        rimPower: { value: 2.5 },
        glowIntensity: { value: 1.0 } // Reduced intensity for more natural look
      },
      vertexShader: `
        varying vec3 vNormal;
        varying vec3 vPosition;
        void main() {
          vNormal = normalize(normalMatrix * normal);
          vPosition = position;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 glowColor;
        uniform float rimPower;
        uniform float glowIntensity;
        varying vec3 vNormal;
        varying vec3 vPosition;

        void main() {
          vec3 viewDirection = normalize(cameraPosition - vPosition);
          float rim = 1.0 - max(dot(viewDirection, vNormal), 0.0);
          rim = pow(rim, rimPower);

          float pulse = sin(time * 3.0) * 0.3 + 0.7;
          vec3 finalColor = glowColor * rim * glowIntensity * pulse;

          gl_FragColor = vec4(finalColor, rim * 0.8);
        }
      `,
      transparent: true,
      side: THREE.BackSide,
      blending: THREE.AdditiveBlending
    });

    // Create main irregular gem mesh - ultra-brilliant like reference
    const gemGeometry = createIrregularGemGeometry();
    const gemMesh = new THREE.Mesh(gemGeometry, gemMaterial);
    gemMesh.castShadow = true;
    gemMesh.receiveShadow = true;
    crystalGroup.add(gemMesh);

    // Create edge glow effect (like the bright edges in reference)
    const edgeGlowGeometry = gemGeometry.clone();
    edgeGlowGeometry.scale(1.02, 1.02, 1.02); // Slightly larger
    const edgeGlowMesh = new THREE.Mesh(edgeGlowGeometry, edgeGlowMaterial);
    crystalGroup.add(edgeGlowMesh);

    // Store reference for animation
    const edgeGlowRef = edgeGlowMesh;

    // Create complex particle systems for tech effect

    // Data particles flowing around
    const dataParticleCount = 200;
    const dataParticleGeometry = new THREE.BufferGeometry();
    const dataPositions = new Float32Array(dataParticleCount * 3);
    const dataColors = new Float32Array(dataParticleCount * 3);
    const dataSizes = new Float32Array(dataParticleCount);

    for (let i = 0; i < dataParticleCount; i++) {
      const radius = 3.5 + Math.random() * 2;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      dataPositions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      dataPositions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      dataPositions[i * 3 + 2] = radius * Math.cos(phi);

      // Emerald spectrum colors - deeper, more natural greens
      const hue = 0.35 + Math.random() * 0.15; // Narrower emerald green range
      const color = new THREE.Color().setHSL(hue, 0.7, 0.4); // Less saturated, darker
      dataColors[i * 3] = color.r;
      dataColors[i * 3 + 1] = color.g;
      dataColors[i * 3 + 2] = color.b;

      dataSizes[i] = Math.random() * 0.15 + 0.05;
    }

    dataParticleGeometry.setAttribute('position', new THREE.BufferAttribute(dataPositions, 3));
    dataParticleGeometry.setAttribute('color', new THREE.BufferAttribute(dataColors, 3));
    dataParticleGeometry.setAttribute('size', new THREE.BufferAttribute(dataSizes, 1));

    const dataParticleMaterial = new THREE.PointsMaterial({
      size: 0.1,
      transparent: true,
      opacity: 0.8,
      sizeAttenuation: true,
      vertexColors: true,
      blending: THREE.AdditiveBlending
    });

    const dataParticles = new THREE.Points(dataParticleGeometry, dataParticleMaterial);
    scene.add(dataParticles);

    // Remove light rays - they were causing white strokes

    // Ultra-intense lighting system like reference image
    const ambientLight = new THREE.AmbientLight(0x001122, 0.2); // Dark ambient for contrast
    scene.add(ambientLight);

    // Extremely bright key lights for maximum brilliance
    const keyLight = new THREE.DirectionalLight(0xffffff, 8.0);
    keyLight.position.set(12, 20, 10);
    keyLight.castShadow = true;
    keyLight.shadow.mapSize.width = 4096;
    keyLight.shadow.mapSize.height = 4096;
    keyLight.shadow.camera.near = 0.1;
    keyLight.shadow.camera.far = 100;
    keyLight.shadow.bias = -0.0001;
    keyLight.shadow.normalBias = 0.02;
    scene.add(keyLight);

    // Multiple cross lights for complex reflections
    const keyLight2 = new THREE.DirectionalLight(0xffffff, 6.0);
    keyLight2.position.set(-12, 15, -8);
    keyLight2.castShadow = true;
    keyLight2.shadow.mapSize.width = 2048;
    keyLight2.shadow.mapSize.height = 2048;
    scene.add(keyLight2);

    const keyLight3 = new THREE.DirectionalLight(0xffffff, 5.0);
    keyLight3.position.set(0, -15, 12);
    scene.add(keyLight3);

    // Natural emerald accent lights
    const fillLight = new THREE.DirectionalLight(0x50C878, 2.5);
    fillLight.position.set(-10, 12, -8);
    scene.add(fillLight);

    // Multiple rim lights for edge definition
    const rimLight1 = new THREE.DirectionalLight(0x006C46, 2.0);
    rimLight1.position.set(0, 0, -20);
    scene.add(rimLight1);

    const rimLight2 = new THREE.DirectionalLight(0xaaffdd, 2.0);
    rimLight2.position.set(0, -15, 0);
    scene.add(rimLight2);

    // Ultra-bright point lights for intense internal glow
    const sparkleLight1 = new THREE.PointLight(0xffffff, 15.0, 35);
    sparkleLight1.position.set(10, 10, 12);
    scene.add(sparkleLight1);

    const sparkleLight2 = new THREE.PointLight(0x00ff88, 12.0, 35);
    sparkleLight2.position.set(-10, -10, 12);
    scene.add(sparkleLight2);

    const sparkleLight3 = new THREE.PointLight(0x44ffaa, 10.0, 30);
    sparkleLight3.position.set(0, 12, -8);
    scene.add(sparkleLight3);

    const sparkleLight4 = new THREE.PointLight(0x88ffcc, 8.0, 25);
    sparkleLight4.position.set(8, -8, -10);
    scene.add(sparkleLight4);

    // Intense spot lights for dramatic highlights
    const spotLight1 = new THREE.SpotLight(0xffffff, 20.0, 50, Math.PI / 6, 0.05);
    spotLight1.position.set(15, 15, 15);
    spotLight1.target.position.set(0, 0, 0);
    scene.add(spotLight1);
    scene.add(spotLight1.target);

    const spotLight2 = new THREE.SpotLight(0x50C878, 12.0, 50, Math.PI / 8, 0.05);
    spotLight2.position.set(-15, -15, 15);
    spotLight2.target.position.set(0, 0, 0);
    scene.add(spotLight2);
    scene.add(spotLight2.target);

    // GSAP Animations - Futuristic floating diamond

    // Main diamond rotation (slow and elegant)
    gsap.to(crystalGroup.rotation, {
      y: Math.PI * 2,
      duration: 20,
      ease: "none",
      repeat: -1
    });

    // Complex floating motion (figure-8 pattern)
    gsap.to(crystalGroup.position, {
      y: 0.5,
      duration: 6,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(crystalGroup.position, {
      x: 0.3,
      duration: 8,
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1
    });

    // Breathing scale animation
    gsap.to(crystalGroup.scale, {
      x: 1.08,
      y: 1.08,
      z: 1.08,
      duration: 4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // No inner core or field animations - clean diamond only

    // Data particles orbital motion
    gsap.to(dataParticles.rotation, {
      y: Math.PI * 2,
      duration: 30,
      ease: "none",
      repeat: -1
    });



    // Ultra-dynamic light animations for reference-level brilliance
    gsap.to(sparkleLight1, {
      intensity: 25.0,
      duration: 1.5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(sparkleLight2, {
      intensity: 20.0,
      duration: 2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 0.3
    });

    gsap.to(sparkleLight3, {
      intensity: 18.0,
      duration: 2.5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 0.7
    });

    gsap.to(sparkleLight4, {
      intensity: 15.0,
      duration: 3,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 1.2
    });

    // Rim lights for edge brilliance
    gsap.to(rimLight1, {
      intensity: 4.0,
      duration: 3.5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(rimLight2, {
      intensity: 3.0,
      duration: 4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 1.5
    });

    // Intense spot light variations for dramatic effect
    gsap.to(spotLight1, {
      intensity: 35.0,
      duration: 2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 0.5
    });

    gsap.to(spotLight2, {
      intensity: 25.0,
      duration: 2.8,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 1.5
    });

    // Dynamic key light movement for changing reflections
    gsap.to(keyLight.position, {
      x: 15,
      y: 25,
      z: 12,
      duration: 8,
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(keyLight2.position, {
      x: -15,
      y: 18,
      z: -10,
      duration: 10,
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1,
      delay: 4
    });

    // Spot light movement for dynamic highlights
    gsap.to(spotLight1.position, {
      x: 18,
      y: 18,
      z: 18,
      duration: 6,
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1
    });

    gsap.to(spotLight2.position, {
      x: -18,
      y: -18,
      z: 18,
      duration: 7,
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1,
      delay: 3
    });

    // Render loop with data flow effects
    let time = 0;
    const animate = () => {
      requestAnimationFrame(animate);
      time += 0.016; // ~60fps

      // Update data texture for flowing code effect
      if (time % 0.1 < 0.016) { // Update every 6 frames
        const newDataTexture = createDataCodeTexture();
        gemMaterial.map = newDataTexture;
        gemMaterial.needsUpdate = true;
      }

      // Particle system micro-movements
      const positions = dataParticleGeometry.attributes.position.array as Float32Array;
      for (let i = 0; i < positions.length; i += 3) {
        positions[i + 1] += Math.sin(time * 2 + i) * 0.002; // Subtle vertical flow
      }
      dataParticleGeometry.attributes.position.needsUpdate = true;

      // Update edge glow shader time for pulsing effect like reference
      if (edgeGlowMaterial.uniforms) {
        edgeGlowMaterial.uniforms.time.value = time;
      }

      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return;

      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;

      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      // Dispose of geometries and materials
      gemGeometry.dispose();
      gemMaterial.dispose();
      edgeGlowMaterial.dispose();
      dataTexture.dispose();
      dataParticleGeometry.dispose();
      dataParticleMaterial.dispose();

      // Remove renderer
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();

      // Kill all GSAP animations
      gsap.killTweensOf(crystalGroup.rotation);
      gsap.killTweensOf(crystalGroup.position);
      gsap.killTweensOf(crystalGroup.scale);
      gsap.killTweensOf(dataParticles.rotation);
      gsap.killTweensOf(sparkleLight1);
      gsap.killTweensOf(sparkleLight2);
      gsap.killTweensOf(sparkleLight3);
      gsap.killTweensOf(sparkleLight4);
      gsap.killTweensOf(rimLight1);
      gsap.killTweensOf(rimLight2);
      gsap.killTweensOf(spotLight1);
      gsap.killTweensOf(spotLight2);
      gsap.killTweensOf(keyLight.position);
      gsap.killTweensOf(keyLight2.position);
      gsap.killTweensOf(spotLight1.position);
      gsap.killTweensOf(spotLight2.position);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="w-full h-full"
      style={{ minHeight: '400px' }}
    />
  );
};

export default CrystalGem;

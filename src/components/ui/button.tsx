import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        "glass": "bg-white/10 backdrop-blur-md text-white border border-white/20 shadow-lg hover:bg-white/20 hover:border-white/30 transition-all duration-300 hover:shadow-white/10 hover:shadow-lg",
        "frost": "bg-white/95 backdrop-blur-sm text-[#40826D] border border-white/40 shadow-lg hover:bg-white hover:shadow-xl transition-all duration-300 font-bold",
        "elegant": "bg-gradient-to-r from-white to-gray-50 text-[#40826D] border border-gray-200 shadow-md hover:shadow-lg hover:from-gray-50 hover:to-white transition-all duration-300 font-medium",
        "hologram": "bg-gradient-to-r from-[#00ffaa] via-[#00d4aa] to-[#00ffaa] text-black font-bold shadow-lg shadow-[#00ffaa]/25 hover:shadow-xl hover:shadow-[#00ffaa]/40 hover:scale-105 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700",
        "crystal": "bg-gradient-to-br from-[#1a2f2a] via-[#40826D] to-[#0d1b17] text-white border border-[#00ffaa]/30 shadow-lg shadow-[#40826D]/25 hover:shadow-xl hover:shadow-[#00ffaa]/40 hover:border-[#00ffaa] before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-[#00ffaa]/10 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-500",
        "emerald": "bg-gradient-to-r from-[#40826D] to-[#4a9d7a] text-white font-bold shadow-lg shadow-[#40826D]/30 hover:shadow-xl hover:shadow-[#40826D]/50 hover:scale-105 transition-all duration-300",
        "light-elegant": "bg-white text-[#40826D] border-2 border-[#40826D]/20 shadow-md hover:border-[#40826D] hover:shadow-lg hover:bg-gray-50 transition-all duration-300 font-bold",
        "soft-glow": "bg-gray-50 text-[#40826D] border border-gray-200 shadow-sm hover:shadow-md hover:bg-white hover:border-[#40826D]/30 transition-all duration-300 font-medium",
        "premium": "bg-gradient-to-r from-gray-800 to-gray-900 text-white shadow-lg hover:from-gray-700 hover:to-gray-800 hover:shadow-xl transition-all duration-300 font-bold",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }

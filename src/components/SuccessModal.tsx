import React from 'react';
import { useTranslation } from 'react-i18next';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CheckCircle, X } from 'lucide-react';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'founder' | 'investor';
}

const SuccessModal: React.FC<SuccessModalProps> = ({ isOpen, onClose, type }) => {
  const { t } = useTranslation();

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md p-0 bg-white">
        <DialogHeader className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] text-white p-6 rounded-t-lg relative">
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 text-white/80 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
          <div className="text-center">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <DialogTitle className="text-2xl font-bold">
              {t('successModal.title')}
            </DialogTitle>
          </div>
        </DialogHeader>
        
        <div className="p-6">
          <div className="text-center space-y-4">
            <p className="text-gray-600 text-lg">
              {type === 'founder' 
                ? t('successModal.founderMessage')
                : t('successModal.investorMessage')
              }
            </p>
            
            {/* <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                {t('successModal.nextSteps')}
              </h4>
              <ul className="text-sm text-gray-600 space-y-1 text-left">
                <li>• {t('successModal.step1')}</li>
                <li>• {t('successModal.step2')}</li>
                <li>• {t('successModal.step3')}</li>
              </ul>
            </div> */}
            
            <div className="flex space-x-3 pt-4">
              <Button
                onClick={handleClose}
                className="flex-1 bg-gradient-to-r from-[#40826D] to-[#2D5A4A] hover:from-[#2D5A4A] hover:to-[#40826D] text-white font-semibold"
              >
                {t('successModal.close')}
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/'}
                className="flex-1 border-gray-300 text-gray-600 hover:bg-gray-50"
              >
                {t('successModal.backToHome')}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SuccessModal;

import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    proxy: {
      // Proxy NocoDB API requests to avoid mixed content issues
      '/api/nocodb': {
        target: 'http://************:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/nocodb/, '/api/v1/db/data/noco/VeridianVista'),
        secure: false,
      },
    },
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
